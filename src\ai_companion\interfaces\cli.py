"""
Command Line Interface for the AI Companion System.
Provides developer tools and system management capabilities.
"""

import asyncio
import logging
import json
import sys
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.markdown import Markdown

from ..main import AICompanionSystem
from ..core.models import InteractionType, EmotionType, MentalHealthRisk
from ..config.settings import settings
from ..utils.helpers import generate_id, utc_now


# Rich console for beautiful output
console = Console()


class CLIInterface:
    """Command line interface for AI companion system."""
    
    def __init__(self):
        """Initialize CLI interface."""
        self.system: Optional[AICompanionSystem] = None
        self.logger = logging.getLogger(__name__)
        self.session_active = False
    
    async def initialize_system(self):
        """Initialize the AI companion system."""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Initializing AI Companion System...", total=None)
                
                self.system = AICompanionSystem()
                await self.system.initialize()
                
                progress.update(task, description="✅ System initialized successfully!")
            
            return True
            
        except Exception as e:
            console.print(f"❌ Failed to initialize system: {e}", style="red")
            return False
    
    async def interactive_chat(self, user_id: str = None):
        """Start interactive chat session."""
        if not self.system:
            console.print("❌ System not initialized. Run 'init' command first.", style="red")
            return
        
        user_id = user_id or Prompt.ask("Enter your user ID", default="cli_user")
        
        console.print(Panel.fit(
            "🤖 AI Companion Interactive Chat\n\n"
            "Welcome! I'm here to listen and support you.\n"
            "Type 'quit' to exit, 'help' for commands, or just start chatting!",
            title="AI Companion",
            border_style="blue"
        ))
        
        self.session_active = True
        conversation_count = 0
        
        try:
            while self.session_active:
                # Get user input
                user_input = Prompt.ask(f"\n[bold blue]You[/bold blue]")
                
                if not user_input.strip():
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    self._show_chat_help()
                    continue
                elif user_input.lower() == 'stats':
                    await self._show_session_stats(user_id, conversation_count)
                    continue
                elif user_input.lower() == 'clear':
                    console.clear()
                    continue
                
                # Process message
                with Progress(
                    SpinnerColumn(),
                    TextColumn("Thinking..."),
                    console=console
                ) as progress:
                    task = progress.add_task("", total=None)
                    
                    response = await self.system.conversation_service.process_message(
                        user_id=user_id,
                        message=user_input,
                        interaction_type=InteractionType.CONVERSATION,
                        context={"interface": "cli"}
                    )
                
                # Display response
                if response:
                    ai_response = response.get("response", "I'm sorry, I couldn't process that.")
                    emotional_state = response.get("emotional_state", {})
                    risk_level = response.get("risk_level", "low")
                    
                    # Show AI response
                    console.print(f"\n[bold green]AI Companion[/bold green]: {ai_response}")
                    
                    # Show emotional analysis if significant
                    if emotional_state.get("intensity", 0) > 0.6:
                        emotion = emotional_state.get("primary_emotion", "neutral")
                        intensity = emotional_state.get("intensity", 0)
                        console.print(f"[dim]Detected emotion: {emotion} (intensity: {intensity:.2f})[/dim]")
                    
                    # Show risk level if elevated
                    if risk_level in ["high", "critical"]:
                        console.print(f"[red]⚠️ Risk level: {risk_level.upper()}[/red]")
                        if risk_level == "critical":
                            console.print("[red]🚨 If you're in immediate danger, please call emergency services (911)[/red]")
                    
                    conversation_count += 1
                else:
                    console.print("[red]❌ Failed to get response from AI companion[/red]")
        
        except KeyboardInterrupt:
            console.print("\n\n👋 Chat session ended. Take care!")
        except Exception as e:
            console.print(f"\n❌ Error during chat: {e}", style="red")
        finally:
            self.session_active = False
    
    def _show_chat_help(self):
        """Show chat help commands."""
        help_text = """
## Chat Commands

- **quit/exit/q** - End the chat session
- **help** - Show this help message
- **stats** - Show session statistics
- **clear** - Clear the screen

## Tips

- Be open and honest about your feelings
- The AI is here to listen and support you
- If you're in crisis, please seek immediate professional help
- Your conversations are private and secure
        """
        console.print(Markdown(help_text))
    
    async def _show_session_stats(self, user_id: str, conversation_count: int):
        """Show session statistics."""
        try:
            # Get user emotional summary
            emotional_summary = self.system.emotional_intelligence.get_user_emotional_summary(user_id)
            
            table = Table(title="Session Statistics")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Messages in session", str(conversation_count))
            table.add_row("Total interactions", str(emotional_summary.get("total_interactions", 0)))
            
            if emotional_summary.get("recent_emotions"):
                recent_emotions = ", ".join(emotional_summary["recent_emotions"][-5:])
                table.add_row("Recent emotions", recent_emotions)
            
            console.print(table)
            
        except Exception as e:
            console.print(f"❌ Error getting stats: {e}", style="red")
    
    async def system_status(self):
        """Show system status."""
        if not self.system:
            console.print("❌ System not initialized", style="red")
            return
        
        try:
            # Get system stats
            storage_stats = await self.system.storage_service.get_storage_stats()
            
            # Create status table
            table = Table(title="AI Companion System Status")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Details")
            
            # System components
            table.add_row("System", "✅ Running", f"Ready: {self.system.system_ready}")
            table.add_row("Gemini API", "✅ Connected", f"Model: {settings.gemini_model}")
            table.add_row("Database", "✅ Connected", f"Size: {storage_stats.get('database_size_mb', 0):.2f} MB")
            
            if settings.redis_enabled:
                table.add_row("Redis Cache", "✅ Connected", f"Host: {settings.redis_host}")
            else:
                table.add_row("Redis Cache", "⚠️ Disabled", "Caching unavailable")
            
            # Data statistics
            table.add_row("User Profiles", "📊", str(storage_stats.get("user_profiles_count", 0)))
            table.add_row("Personal Memories", "📊", str(storage_stats.get("personal_memories_count", 0)))
            table.add_row("Universal Memories", "📊", str(storage_stats.get("universal_memories_count", 0)))
            table.add_row("Conversations", "📊", str(storage_stats.get("conversations_count", 0)))
            
            console.print(table)
            
        except Exception as e:
            console.print(f"❌ Error getting system status: {e}", style="red")
    
    async def test_components(self):
        """Test system components."""
        if not self.system:
            console.print("❌ System not initialized", style="red")
            return
        
        console.print("🧪 Testing system components...\n")
        
        tests = [
            ("Gemini API", self._test_gemini),
            ("Memory Service", self._test_memory),
            ("Emotional Intelligence", self._test_emotions),
            ("Storage Service", self._test_storage)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                with Progress(
                    SpinnerColumn(),
                    TextColumn(f"Testing {test_name}..."),
                    console=console
                ) as progress:
                    task = progress.add_task("", total=None)
                    result = await test_func()
                    results[test_name] = result
                
                if result:
                    console.print(f"✅ {test_name}: PASSED")
                else:
                    console.print(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                console.print(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        console.print(f"\n📊 Test Results: {passed}/{total} passed")
        
        if passed == total:
            console.print("🎉 All tests passed! System is ready.", style="green")
        else:
            console.print("⚠️ Some tests failed. Check system configuration.", style="yellow")
    
    async def _test_gemini(self) -> bool:
        """Test Gemini API."""
        try:
            response = await self.system.gemini_service.generate_response("Hello, this is a test.")
            return bool(response and len(response) > 0)
        except Exception:
            return False
    
    async def _test_memory(self) -> bool:
        """Test memory service."""
        try:
            test_memory = await self.system.memory_service.store_memory(
                user_id="test_user",
                content="Test memory",
                interaction_type="conversation"
            )
            return bool(test_memory)
        except Exception:
            return False
    
    async def _test_emotions(self) -> bool:
        """Test emotional intelligence."""
        try:
            emotion = await self.system.emotional_intelligence.analyze_emotion("I'm feeling happy today!")
            return bool(emotion and emotion.primary_emotion)
        except Exception:
            return False
    
    async def _test_storage(self) -> bool:
        """Test storage service."""
        try:
            stats = await self.system.storage_service.get_storage_stats()
            return bool(stats)
        except Exception:
            return False
    
    async def export_data(self, user_id: str, output_file: str):
        """Export user data."""
        if not self.system:
            console.print("❌ System not initialized", style="red")
            return
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("Exporting data..."),
                console=console
            ) as progress:
                task = progress.add_task("", total=None)
                
                # Get user data
                profile = await self.system.storage_service.get_user_profile(user_id)
                memories = await self.system.memory_service.get_personal_memories(user_id, limit=1000)
                emotional_summary = self.system.emotional_intelligence.get_user_emotional_summary(user_id)
                
                # Prepare export data
                export_data = {
                    "user_id": user_id,
                    "export_timestamp": utc_now().isoformat(),
                    "profile": profile.model_dump() if profile else None,
                    "memories": [memory.model_dump() for memory in memories],
                    "emotional_summary": emotional_summary,
                    "total_memories": len(memories)
                }
                
                # Write to file
                output_path = Path(output_file)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
            
            console.print(f"✅ Data exported to {output_file}", style="green")
            
        except Exception as e:
            console.print(f"❌ Error exporting data: {e}", style="red")
    
    async def cleanup_data(self, days_old: int = 90):
        """Clean up old data."""
        if not self.system:
            console.print("❌ System not initialized", style="red")
            return
        
        if not Confirm.ask(f"Are you sure you want to delete data older than {days_old} days?"):
            console.print("❌ Cleanup cancelled")
            return
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("Cleaning up old data..."),
                console=console
            ) as progress:
                task = progress.add_task("", total=None)
                await self.system.storage_service.cleanup_old_data(days_old)
            
            console.print(f"✅ Cleaned up data older than {days_old} days", style="green")
            
        except Exception as e:
            console.print(f"❌ Error during cleanup: {e}", style="red")
    
    async def shutdown(self):
        """Shutdown the system."""
        if self.system:
            await self.system.shutdown()
            console.print("👋 System shutdown complete")


# Click CLI commands
cli = CLIInterface()


@click.group()
def main():
    """AI Companion System CLI - Developer tools and system management."""
    pass


@main.command()
async def init():
    """Initialize the AI companion system."""
    success = await cli.initialize_system()
    if not success:
        sys.exit(1)


@main.command()
@click.option('--user-id', default=None, help='User ID for the chat session')
async def chat(user_id):
    """Start interactive chat session."""
    await cli.interactive_chat(user_id)


@main.command()
async def status():
    """Show system status."""
    await cli.system_status()


@main.command()
async def test():
    """Test system components."""
    await cli.test_components()


@main.command()
@click.argument('user_id')
@click.argument('output_file')
async def export(user_id, output_file):
    """Export user data to file."""
    await cli.export_data(user_id, output_file)


@main.command()
@click.option('--days', default=90, help='Delete data older than this many days')
async def cleanup(days):
    """Clean up old data."""
    await cli.cleanup_data(days)


@main.command()
async def shutdown():
    """Shutdown the system."""
    await cli.shutdown()


# Async wrapper for Click
def async_command(f):
    """Decorator to make Click commands async."""
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# Apply async wrapper to all commands
for command in main.commands.values():
    command.callback = async_command(command.callback)


if __name__ == "__main__":
    main()
