# AI Companion System - Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG_MODE=true
LOG_LEVEL=INFO

# =============================================================================
# GEMINI API CONFIGURATION
# =============================================================================

# Google Gemini API Key (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# Gemini Model Configuration
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7
GEMINI_TOP_P=0.9
GEMINI_TOP_K=40
GEMINI_MAX_TOKENS=2048

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Path (SQLite)
DATABASE_PATH=data/db/ai_companion.db

# Redis Configuration (Optional - for caching)
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API Settings
API_PORT=8000
API_KEY_REQUIRED=false
API_KEY=your_secure_api_key_here

# CORS Settings (comma-separated origins)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000

# =============================================================================
# INTERFACE CONFIGURATION
# =============================================================================

# Gradio Web Interface
GRADIO_PORT=7860
GRADIO_SHARE=false

# =============================================================================
# WHATSAPP INTEGRATION
# =============================================================================

# WhatsApp Business API Configuration
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# =============================================================================
# MENTAL HEALTH CONFIGURATION
# =============================================================================

# Crisis Detection Thresholds
CRISIS_THRESHOLD=0.7
MODERATE_RISK_THRESHOLD=0.4

# Mental Health Platform Settings
ENABLE_MENTAL_HEALTH_PLATFORM=true
MIN_COHORT_SIZE=10
ANONYMIZATION_ENABLED=true
K_ANONYMITY_LEVEL=5

# =============================================================================
# PRIVACY & SECURITY
# =============================================================================

# Encryption Key for Sensitive Data (Generate a secure key)
ENCRYPTION_KEY=your_encryption_key_here

# Data Retention (days)
DATA_RETENTION_DAYS=365
CLEANUP_INTERVAL_HOURS=24

# =============================================================================
# MEMORY CONFIGURATION
# =============================================================================

# Memory Management
MAX_PERSONAL_MEMORIES=1000
MAX_UNIVERSAL_MEMORIES=500
MEMORY_IMPORTANCE_THRESHOLD=0.3
MEMORY_DECAY_FACTOR=0.95

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=60
MAX_REQUESTS_PER_HOUR=1000

# Caching
CACHE_TTL_SECONDS=3600
ENABLE_RESPONSE_CACHING=true

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Logging Configuration
LOG_FILE_PATH=data/logs/ai_companion.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_PATH=data/logs/performance.log

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development Features
ENABLE_DEBUG_ENDPOINTS=true
ENABLE_TEST_MODE=false
MOCK_EXTERNAL_APIS=false

# Testing
TEST_USER_ID=test_user
TEST_DATABASE_PATH=data/db/test_ai_companion.db

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Server Configuration
HOST=0.0.0.0
WORKERS=1
TIMEOUT=300

# Health Check
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=30

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# Email Notifications (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# Webhook URLs (Optional)
CRISIS_ALERT_WEBHOOK=
ANALYTICS_WEBHOOK=

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
ENABLE_WHATSAPP_BOT=false
ENABLE_CRISIS_DETECTION=true
ENABLE_ANALYTICS=true
ENABLE_MEMORY_SHARING=false
ENABLE_VOICE_PROCESSING=false

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# AI Model Fine-tuning
ENABLE_MODEL_FINE_TUNING=false
FINE_TUNING_DATA_PATH=data/fine_tuning/

# Custom Therapeutic Techniques
CUSTOM_TECHNIQUES_PATH=data/custom_techniques.json

# Multi-language Support
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all placeholder values with your actual configuration
# 2. Keep this file secure and never commit it to version control
# 3. For production, use environment-specific values
# 4. Some features require additional setup (WhatsApp, Redis, etc.)
# 5. Refer to the documentation for detailed configuration instructions
