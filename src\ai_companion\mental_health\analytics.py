"""
Mental Health Analytics Service for the AI Companion System.
Provides anonymized insights and data analytics for mental health research.
"""

import asyncio
import logging
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass

import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from ..core.models import (
    EmotionType, MentalHealthRisk, InteractionType,
    EmotionalState, MemoryEntry, utc_now
)
from ..services.storage import StorageService
from .privacy import DataAnonymizer
from ..config.settings import settings
from ..utils.helpers import generate_id


@dataclass
class AnalyticsInsight:
    """Data class for analytics insights."""
    insight_id: str
    title: str
    description: str
    data: Dict[str, Any]
    confidence: float
    timestamp: datetime
    category: str


class MentalHealthAnalytics:
    """Advanced mental health analytics with privacy-first design."""
    
    def __init__(self, storage_service: StorageService):
        """Initialize mental health analytics service."""
        self.logger = logging.getLogger(__name__)
        self.storage_service = storage_service
        self.anonymizer = DataAnonymizer()
        
        # Analytics cache
        self.insights_cache: Dict[str, List[AnalyticsInsight]] = defaultdict(list)
        self.aggregated_data: Dict[str, Any] = {}
        
        # Minimum cohort sizes for privacy
        self.min_cohort_size = settings.min_cohort_size
        
        self.logger.info("✅ Mental Health Analytics Service initialized")
    
    async def generate_insights(
        self,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate mental health insights with privacy protection."""
        try:
            # Set default date range
            if not end_date:
                end_date = utc_now()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Determine analysis type
            if user_id:
                # Individual user analysis
                return await self._generate_individual_insights(user_id, start_date, end_date, filters)
            else:
                # Population-level analysis (anonymized)
                return await self._generate_population_insights(start_date, end_date, filters)
                
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return {"error": str(e), "timestamp": utc_now()}
    
    async def _generate_individual_insights(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate insights for individual user."""
        try:
            # Get user data
            memories = await self.storage_service.get_personal_memories(user_id, limit=1000)
            
            # Filter by date range
            filtered_memories = [
                memory for memory in memories
                if start_date <= memory.timestamp <= end_date
            ]
            
            if not filtered_memories:
                return {"message": "No data available for the specified period"}
            
            # Analyze emotional patterns
            emotional_insights = self._analyze_emotional_patterns(filtered_memories)
            
            # Analyze interaction patterns
            interaction_insights = self._analyze_interaction_patterns(filtered_memories)
            
            # Analyze progress trends
            progress_insights = self._analyze_progress_trends(filtered_memories)
            
            # Generate recommendations
            recommendations = self._generate_individual_recommendations(
                emotional_insights, interaction_insights, progress_insights
            )
            
            return {
                "user_id": user_id,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "emotional_insights": emotional_insights,
                "interaction_insights": interaction_insights,
                "progress_insights": progress_insights,
                "recommendations": recommendations,
                "total_interactions": len(filtered_memories),
                "generated_at": utc_now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating individual insights: {e}")
            return {"error": str(e)}
    
    async def _generate_population_insights(
        self,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate anonymized population-level insights."""
        try:
            # Get aggregated data (anonymized)
            aggregated_data = await self._get_aggregated_data(start_date, end_date, filters)
            
            if not aggregated_data or len(aggregated_data.get("user_cohorts", [])) < self.min_cohort_size:
                return {
                    "message": f"Insufficient data for population analysis (minimum {self.min_cohort_size} users required)",
                    "privacy_protected": True
                }
            
            # Analyze population trends
            emotion_trends = self._analyze_population_emotion_trends(aggregated_data)
            
            # Analyze risk patterns
            risk_patterns = self._analyze_population_risk_patterns(aggregated_data)
            
            # Analyze intervention effectiveness
            intervention_effectiveness = self._analyze_intervention_effectiveness(aggregated_data)
            
            # Generate population insights
            population_insights = self._generate_population_recommendations(
                emotion_trends, risk_patterns, intervention_effectiveness
            )
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "cohort_size": len(aggregated_data.get("user_cohorts", [])),
                "emotion_trends": emotion_trends,
                "risk_patterns": risk_patterns,
                "intervention_effectiveness": intervention_effectiveness,
                "population_insights": population_insights,
                "privacy_protected": True,
                "anonymization_level": f"k-{settings.k_anonymity_level}",
                "generated_at": utc_now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating population insights: {e}")
            return {"error": str(e)}
    
    def _analyze_emotional_patterns(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze emotional patterns in user memories."""
        try:
            emotions = []
            intensities = []
            timestamps = []
            
            for memory in memories:
                emotional_context = memory.emotional_context
                if emotional_context:
                    emotion = emotional_context.get("primary_emotion")
                    intensity = emotional_context.get("intensity", 0.5)
                    
                    if emotion:
                        emotions.append(emotion)
                        intensities.append(intensity)
                        timestamps.append(memory.timestamp)
            
            if not emotions:
                return {"message": "No emotional data available"}
            
            # Emotion frequency analysis
            emotion_counts = Counter(emotions)
            emotion_distribution = {
                emotion: count / len(emotions)
                for emotion, count in emotion_counts.items()
            }
            
            # Intensity analysis
            avg_intensity = np.mean(intensities)
            intensity_trend = self._calculate_trend(intensities, timestamps)
            
            # Emotional volatility
            volatility = np.std(intensities) if len(intensities) > 1 else 0.0
            
            # Dominant emotion periods
            dominant_periods = self._identify_dominant_emotion_periods(emotions, timestamps)
            
            return {
                "emotion_distribution": emotion_distribution,
                "average_intensity": avg_intensity,
                "intensity_trend": intensity_trend,
                "emotional_volatility": volatility,
                "dominant_periods": dominant_periods,
                "most_common_emotion": emotion_counts.most_common(1)[0] if emotion_counts else None
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotional patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_interaction_patterns(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze interaction patterns."""
        try:
            interaction_types = [memory.interaction_type for memory in memories]
            timestamps = [memory.timestamp for memory in memories]
            
            # Interaction frequency
            interaction_counts = Counter(interaction_types)
            
            # Temporal patterns
            hourly_patterns = self._analyze_temporal_patterns(timestamps, "hour")
            daily_patterns = self._analyze_temporal_patterns(timestamps, "day")
            
            # Conversation length trends
            conversation_lengths = self._analyze_conversation_lengths(memories)
            
            # Engagement metrics
            engagement_score = self._calculate_engagement_score(memories)
            
            return {
                "interaction_distribution": dict(interaction_counts),
                "hourly_patterns": hourly_patterns,
                "daily_patterns": daily_patterns,
                "conversation_metrics": conversation_lengths,
                "engagement_score": engagement_score,
                "total_interactions": len(memories)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing interaction patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_progress_trends(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze progress and improvement trends."""
        try:
            # Sort memories by timestamp
            sorted_memories = sorted(memories, key=lambda m: m.timestamp)
            
            # Extract emotional data over time
            emotional_timeline = []
            for memory in sorted_memories:
                emotional_context = memory.emotional_context
                if emotional_context:
                    emotional_timeline.append({
                        "timestamp": memory.timestamp,
                        "emotion": emotional_context.get("primary_emotion"),
                        "intensity": emotional_context.get("intensity", 0.5),
                        "risk_level": emotional_context.get("risk_level", "low")
                    })
            
            if len(emotional_timeline) < 3:
                return {"message": "Insufficient data for trend analysis"}
            
            # Calculate trends
            intensity_trend = self._calculate_emotional_trend(emotional_timeline)
            risk_trend = self._calculate_risk_trend(emotional_timeline)
            stability_trend = self._calculate_stability_trend(emotional_timeline)
            
            # Identify improvement indicators
            improvement_indicators = self._identify_improvement_indicators(emotional_timeline)
            
            # Calculate progress score
            progress_score = self._calculate_progress_score(
                intensity_trend, risk_trend, stability_trend
            )
            
            return {
                "intensity_trend": intensity_trend,
                "risk_trend": risk_trend,
                "stability_trend": stability_trend,
                "improvement_indicators": improvement_indicators,
                "progress_score": progress_score,
                "timeline_length_days": (sorted_memories[-1].timestamp - sorted_memories[0].timestamp).days
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing progress trends: {e}")
            return {"error": str(e)}
    
    def _calculate_trend(self, values: List[float], timestamps: List[datetime]) -> str:
        """Calculate trend direction for a series of values."""
        if len(values) < 3:
            return "insufficient_data"
        
        # Simple linear trend calculation
        x = np.arange(len(values))
        y = np.array(values)
        
        # Calculate correlation coefficient
        correlation = np.corrcoef(x, y)[0, 1]
        
        if correlation > 0.3:
            return "increasing"
        elif correlation < -0.3:
            return "decreasing"
        else:
            return "stable"
    
    def _identify_dominant_emotion_periods(
        self,
        emotions: List[str],
        timestamps: List[datetime]
    ) -> List[Dict[str, Any]]:
        """Identify periods where specific emotions dominate."""
        try:
            # Group emotions by time windows (e.g., daily)
            daily_emotions = defaultdict(list)
            
            for emotion, timestamp in zip(emotions, timestamps):
                day_key = timestamp.date().isoformat()
                daily_emotions[day_key].append(emotion)
            
            dominant_periods = []
            for day, day_emotions in daily_emotions.items():
                if len(day_emotions) >= 3:  # Minimum interactions per day
                    emotion_counts = Counter(day_emotions)
                    dominant_emotion = emotion_counts.most_common(1)[0]
                    
                    if dominant_emotion[1] / len(day_emotions) >= 0.6:  # 60% threshold
                        dominant_periods.append({
                            "date": day,
                            "dominant_emotion": dominant_emotion[0],
                            "frequency": dominant_emotion[1] / len(day_emotions),
                            "total_interactions": len(day_emotions)
                        })
            
            return dominant_periods
            
        except Exception as e:
            self.logger.error(f"Error identifying dominant emotion periods: {e}")
            return []
    
    def _analyze_temporal_patterns(
        self,
        timestamps: List[datetime],
        granularity: str
    ) -> Dict[str, float]:
        """Analyze temporal patterns in interactions."""
        try:
            if granularity == "hour":
                time_values = [ts.hour for ts in timestamps]
                time_range = range(24)
            elif granularity == "day":
                time_values = [ts.weekday() for ts in timestamps]
                time_range = range(7)
            else:
                return {}
            
            # Count interactions by time unit
            time_counts = Counter(time_values)
            total_interactions = len(timestamps)
            
            # Calculate distribution
            time_distribution = {}
            for time_unit in time_range:
                count = time_counts.get(time_unit, 0)
                time_distribution[str(time_unit)] = count / total_interactions if total_interactions > 0 else 0
            
            return time_distribution
            
        except Exception as e:
            self.logger.error(f"Error analyzing temporal patterns: {e}")
            return {}
    
    def _analyze_conversation_lengths(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze conversation length patterns."""
        try:
            # Group memories by conversation (simplified - using time gaps)
            conversations = []
            current_conversation = []
            
            sorted_memories = sorted(memories, key=lambda m: m.timestamp)
            
            for i, memory in enumerate(sorted_memories):
                if i == 0:
                    current_conversation = [memory]
                else:
                    time_gap = (memory.timestamp - sorted_memories[i-1].timestamp).total_seconds()
                    
                    if time_gap > 3600:  # 1 hour gap = new conversation
                        if current_conversation:
                            conversations.append(current_conversation)
                        current_conversation = [memory]
                    else:
                        current_conversation.append(memory)
            
            if current_conversation:
                conversations.append(current_conversation)
            
            # Analyze conversation metrics
            conversation_lengths = [len(conv) for conv in conversations]
            conversation_durations = []
            
            for conv in conversations:
                if len(conv) > 1:
                    duration = (conv[-1].timestamp - conv[0].timestamp).total_seconds() / 60  # minutes
                    conversation_durations.append(duration)
            
            return {
                "total_conversations": len(conversations),
                "average_length": np.mean(conversation_lengths) if conversation_lengths else 0,
                "average_duration_minutes": np.mean(conversation_durations) if conversation_durations else 0,
                "longest_conversation": max(conversation_lengths) if conversation_lengths else 0,
                "conversation_length_distribution": dict(Counter(conversation_lengths))
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing conversation lengths: {e}")
            return {}
    
    def _calculate_engagement_score(self, memories: List[MemoryEntry]) -> float:
        """Calculate user engagement score."""
        try:
            if not memories:
                return 0.0
            
            # Factors for engagement score
            total_interactions = len(memories)
            avg_importance = np.mean([memory.importance_score for memory in memories])
            
            # Time span
            sorted_memories = sorted(memories, key=lambda m: m.timestamp)
            time_span_days = (sorted_memories[-1].timestamp - sorted_memories[0].timestamp).days
            
            # Interaction frequency
            frequency_score = min(total_interactions / max(time_span_days, 1), 10) / 10  # Cap at 10 per day
            
            # Importance score
            importance_score = avg_importance
            
            # Consistency (how evenly distributed are interactions)
            consistency_score = self._calculate_consistency_score(sorted_memories)
            
            # Combine scores
            engagement_score = (frequency_score * 0.4 + importance_score * 0.4 + consistency_score * 0.2)
            
            return min(engagement_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating engagement score: {e}")
            return 0.0
    
    def _calculate_consistency_score(self, sorted_memories: List[MemoryEntry]) -> float:
        """Calculate consistency of interactions over time."""
        try:
            if len(sorted_memories) < 3:
                return 0.5  # Neutral score for insufficient data
            
            # Calculate time gaps between interactions
            time_gaps = []
            for i in range(1, len(sorted_memories)):
                gap = (sorted_memories[i].timestamp - sorted_memories[i-1].timestamp).total_seconds()
                time_gaps.append(gap)
            
            # Calculate coefficient of variation (lower = more consistent)
            if time_gaps:
                cv = np.std(time_gaps) / np.mean(time_gaps) if np.mean(time_gaps) > 0 else 1.0
                consistency_score = max(0, 1 - min(cv / 2, 1))  # Normalize and invert
                return consistency_score
            
            return 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating consistency score: {e}")
            return 0.5
    
    async def get_platform_summary(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get platform-wide summary statistics."""
        try:
            # Get storage statistics
            storage_stats = await self.storage_service.get_storage_stats()
            
            # Calculate platform metrics
            total_users = storage_stats.get("user_profiles_count", 0)
            total_interactions = storage_stats.get("personal_memories_count", 0)
            total_conversations = storage_stats.get("conversations_count", 0)
            
            # Privacy-protected metrics
            if total_users >= self.min_cohort_size:
                # Get anonymized aggregated data
                aggregated_data = await self._get_aggregated_data(start_date, end_date)
                
                platform_metrics = {
                    "total_users": total_users,
                    "total_interactions": total_interactions,
                    "total_conversations": total_conversations,
                    "average_interactions_per_user": total_interactions / total_users if total_users > 0 else 0,
                    "database_size_mb": storage_stats.get("database_size_mb", 0),
                    "privacy_protected": True,
                    "anonymization_level": f"k-{settings.k_anonymity_level}"
                }
                
                # Add aggregated insights if available
                if aggregated_data:
                    platform_metrics.update({
                        "emotion_trends": self._analyze_population_emotion_trends(aggregated_data),
                        "risk_distribution": self._analyze_population_risk_patterns(aggregated_data)
                    })
            else:
                platform_metrics = {
                    "message": f"Platform summary not available (minimum {self.min_cohort_size} users required)",
                    "privacy_protected": True,
                    "current_users": total_users
                }
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "platform_metrics": platform_metrics,
                "generated_at": utc_now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting platform summary: {e}")
            return {"error": str(e)}
    
    async def _get_aggregated_data(
        self,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get aggregated, anonymized data for population analysis."""
        try:
            # This is a simplified implementation
            # In production, you'd want more sophisticated anonymization
            
            # Get all memories in date range (this would be optimized in production)
            # For now, return empty data structure
            return {
                "user_cohorts": [],
                "anonymized_interactions": [],
                "aggregated_emotions": {},
                "risk_distributions": {}
            }
            
        except Exception as e:
            self.logger.error(f"Error getting aggregated data: {e}")
            return {}
    
    def _analyze_population_emotion_trends(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze emotion trends across population."""
        # Placeholder implementation
        return {
            "trending_emotions": [],
            "emotion_correlations": {},
            "seasonal_patterns": {}
        }
    
    def _analyze_population_risk_patterns(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze risk patterns across population."""
        # Placeholder implementation
        return {
            "risk_distribution": {},
            "risk_factors": [],
            "intervention_triggers": {}
        }
    
    def _analyze_intervention_effectiveness(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze effectiveness of interventions."""
        # Placeholder implementation
        return {
            "intervention_success_rates": {},
            "most_effective_techniques": [],
            "outcome_metrics": {}
        }
    
    def _generate_individual_recommendations(
        self,
        emotional_insights: Dict[str, Any],
        interaction_insights: Dict[str, Any],
        progress_insights: Dict[str, Any]
    ) -> List[str]:
        """Generate personalized recommendations."""
        recommendations = []
        
        # Emotional pattern recommendations
        if emotional_insights.get("emotional_volatility", 0) > 0.7:
            recommendations.append("Consider practicing emotional regulation techniques like mindfulness or deep breathing")
        
        # Engagement recommendations
        engagement_score = interaction_insights.get("engagement_score", 0)
        if engagement_score < 0.3:
            recommendations.append("Try to engage more regularly for better support and progress tracking")
        
        # Progress recommendations
        progress_score = progress_insights.get("progress_score", 0)
        if progress_score < 0.4:
            recommendations.append("Consider discussing your goals and challenges to develop better coping strategies")
        
        return recommendations
    
    def _generate_population_recommendations(
        self,
        emotion_trends: Dict[str, Any],
        risk_patterns: Dict[str, Any],
        intervention_effectiveness: Dict[str, Any]
    ) -> List[str]:
        """Generate population-level recommendations."""
        # Placeholder implementation
        return [
            "Population-level insights available for mental health professionals",
            "Anonymized data shows general trends in emotional well-being",
            "Intervention effectiveness data can inform treatment approaches"
        ]
